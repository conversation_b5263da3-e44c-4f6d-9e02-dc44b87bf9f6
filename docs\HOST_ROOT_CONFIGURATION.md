# Configurações Necessárias no Host Root para Evitar Re-renderizações

## 🎯 **Problema**

Mesmo com otimizações no MFE, o **host root** pode estar causando re-renderizações completas quando:
- Mudanças de estado no host propagam para todos os MFEs
- Hot reload do host remonta todos os MFEs
- Context providers globais re-renderizam

## 🚀 **Configurações Necessárias no Host Root**

### **1. Configuração do Single SPA no Host**

```javascript
// host-root/src/index.js
import { registerApplication, start } from 'single-spa';

registerApplication({
  name: 'CVP-PlataformaCaixa-PosVenda-Previdencia',
  app: () => System.import('CVP-PlataformaCaixa-PosVenda-Previdencia'),
  activeWhen: ['/previdencia'],
  customProps: {
    // Configurações para evitar re-renders
    domElementGetter: () => {
      let element = document.getElementById('previdencia-mfe');
      if (!element) {
        element = document.createElement('div');
        element.id = 'previdencia-mfe';
        document.body.appendChild(element);
      }
      return element;
    },
    // Evitar re-mount desnecessário
    preserveGlobalState: true,
    enableHMR: true,
  }
});

// Configurações globais
start({
  urlRerouteOnly: true, // Evitar re-mount em mudanças de URL
});
```

### **2. Webpack do Host Root**

```javascript
// host-root/webpack.config.js
module.exports = {
  // ... outras configurações
  
  devServer: {
    hot: true,
    liveReload: false, // IMPORTANTE: Desabilitar live reload
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    // Configurações específicas para Single SPA
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  
  optimization: {
    // Evitar re-bundling desnecessário
    runtimeChunk: 'single',
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
};
```

### **3. Context Providers Otimizados no Host**

```typescript
// host-root/src/providers/GlobalProvider.tsx
import React, { useMemo, useCallback } from 'react';

const GlobalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Memoizar valores do context para evitar re-renders
  const contextValue = useMemo(() => ({
    // valores do context
  }), [/* dependências específicas */]);

  // Memoizar callbacks
  const handleGlobalAction = useCallback((action) => {
    // lógica da ação
  }, []);

  return (
    <GlobalContext.Provider value={contextValue}>
      {children}
    </GlobalContext.Provider>
  );
};

// Usar React.memo para o provider
export default React.memo(GlobalProvider);
```

### **4. Configuração de Roteamento**

```typescript
// host-root/src/routing/Router.tsx
import { Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';

// Criar história única para evitar conflitos
const history = createBrowserHistory();

// Configurar para Single SPA
if (window.singleSpaNavigate) {
  history.listen(({ pathname }) => {
    window.singleSpaNavigate(pathname);
  });
}

const AppRouter: React.FC = () => {
  return (
    <Router history={history}>
      {/* rotas do host */}
    </Router>
  );
};
```

### **5. Configuração de HMR no Host**

```javascript
// host-root/src/hmr-config.js
if (module.hot) {
  // Configurar HMR para não afetar MFEs
  module.hot.accept();
  
  // Evitar reload completo
  module.hot.dispose(() => {
    console.log('[HMR] Host disposing, preserving MFE state');
  });
  
  // Configurar para MFEs
  window.__SINGLE_SPA_HMR__ = {
    preserveMFEState: true,
    debounceTime: 100,
  };
}
```

## 🔧 **Configurações de Desenvolvimento**

### **1. Package.json do Host**

```json
{
  "scripts": {
    "start": "webpack serve --mode development --config webpack.config.js",
    "start:single-spa": "concurrently \"npm run start\" \"npm run start:mfe\"",
    "start:mfe": "cd ../CVP-PlataformaCaixa-PosVenda-Previdencia-Front && npm start"
  },
  "devDependencies": {
    "concurrently": "^7.0.0"
  }
}
```

### **2. Variáveis de Ambiente do Host**

```bash
# host-root/.env.development
REACT_APP_ENABLE_HMR=true
REACT_APP_PRESERVE_MFE_STATE=true
REACT_APP_DEBOUNCE_UPDATES=true
```

## 🚨 **Problemas Comuns e Soluções**

### **Problema 1: MFE remonta a cada mudança**

**Causa**: Host está usando `liveReload: true`

**Solução**:
```javascript
// webpack.config.js do host
devServer: {
  hot: true,
  liveReload: false, // ← IMPORTANTE
}
```

### **Problema 2: Context global causa re-renders**

**Causa**: Context providers não memoizados

**Solução**:
```typescript
const contextValue = useMemo(() => ({
  // valores
}), [/* dependências específicas */]);
```

### **Problema 3: Roteamento conflitante**

**Causa**: Múltiplas instâncias de router

**Solução**:
```typescript
// Usar história única compartilhada
const history = createBrowserHistory();
window.__SHARED_HISTORY__ = history;
```

## 📊 **Verificação das Configurações**

### **Checklist para o Host Root**:

- [ ] `liveReload: false` no webpack dev server
- [ ] Context providers memoizados
- [ ] História única para roteamento
- [ ] HMR configurado corretamente
- [ ] `urlRerouteOnly: true` no Single SPA
- [ ] Elementos DOM preservados entre re-renders

### **Teste de Funcionamento**:

1. **Iniciar host e MFE**: Ambos devem carregar sem erros
2. **Fazer mudança no MFE**: Apenas componente específico deve re-renderizar
3. **Verificar console**: Não deve haver warnings de re-mount
4. **Testar navegação**: Deve ser fluida sem recarregar MFEs

## 🎯 **Resultado Esperado**

Com essas configurações no host root:

- ✅ **MFE não remonta** a cada mudança
- ✅ **Hot reload otimizado** apenas para componentes alterados
- ✅ **Estado preservado** durante desenvolvimento
- ✅ **Performance melhorada** significativamente
- ✅ **Experiência de desenvolvimento** fluida

## 📞 **Próximos Passos**

1. **Aplicar configurações** no host root
2. **Testar integração** entre host e MFE
3. **Verificar performance** com React DevTools
4. **Documentar** configurações específicas do projeto

Se mesmo com essas configurações o problema persistir, pode ser necessário revisar a arquitetura de comunicação entre host e MFE ou considerar outras estratégias como Module Federation.
