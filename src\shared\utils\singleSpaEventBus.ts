/**
 * Event Bus otimizado para Single SPA
 * Evita re-renderizações completas do MFE ao comunicar mudanças de estado
 */

interface IEventBusEvent {
  type: string;
  payload?: any;
  source?: string;
  timestamp?: number;
}

interface IEventBusListener {
  (event: IEventBusEvent): void;
}

class SingleSpaEventBus {
  private listeners: Map<string, Set<IEventBusListener>> = new Map();
  private eventHistory: IEventBusEvent[] = [];
  private maxHistorySize = 100;

  /**
   * Registra um listener para um tipo de evento específico
   */
  on(eventType: string, listener: IEventBusListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    this.listeners.get(eventType)!.add(listener);

    // Retorna função para remover o listener
    return () => {
      this.off(eventType, listener);
    };
  }

  /**
   * Remove um listener específico
   */
  off(eventType: string, listener: IEventBusListener): void {
    const eventListeners = this.listeners.get(eventType);
    if (eventListeners) {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(eventType);
      }
    }
  }

  /**
   * Emite um evento para todos os listeners registrados
   */
  emit(eventType: string, payload?: any, source?: string): void {
    const event: IEventBusEvent = {
      type: eventType,
      payload,
      source: source || 'unknown',
      timestamp: Date.now(),
    };

    // Adicionar ao histórico
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // Notificar listeners
    const eventListeners = this.listeners.get(eventType);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.warn(`Erro ao processar evento ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * Emite um evento apenas uma vez (debounced)
   */
  emitOnce(eventType: string, payload?: any, source?: string, delay = 100): void {
    const key = `${eventType}_${source}`;
    
    // Limpar timeout anterior se existir
    if ((this as any)[`_timeout_${key}`]) {
      clearTimeout((this as any)[`_timeout_${key}`]);
    }

    // Criar novo timeout
    (this as any)[`_timeout_${key}`] = setTimeout(() => {
      this.emit(eventType, payload, source);
      delete (this as any)[`_timeout_${key}`];
    }, delay);
  }

  /**
   * Remove todos os listeners de um tipo de evento
   */
  removeAllListeners(eventType?: string): void {
    if (eventType) {
      this.listeners.delete(eventType);
    } else {
      this.listeners.clear();
    }
  }

  /**
   * Obtém o histórico de eventos
   */
  getEventHistory(eventType?: string): IEventBusEvent[] {
    if (eventType) {
      return this.eventHistory.filter(event => event.type === eventType);
    }
    return [...this.eventHistory];
  }

  /**
   * Limpa o histórico de eventos
   */
  clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Verifica se há listeners para um tipo de evento
   */
  hasListeners(eventType: string): boolean {
    const eventListeners = this.listeners.get(eventType);
    return eventListeners ? eventListeners.size > 0 : false;
  }

  /**
   * Obtém estatísticas do event bus
   */
  getStats(): {
    totalListeners: number;
    eventTypes: string[];
    historySize: number;
  } {
    let totalListeners = 0;
    const eventTypes: string[] = [];

    this.listeners.forEach((listeners, eventType) => {
      totalListeners += listeners.size;
      eventTypes.push(eventType);
    });

    return {
      totalListeners,
      eventTypes,
      historySize: this.eventHistory.length,
    };
  }
}

// Instância singleton global para Single SPA
const eventBus = new SingleSpaEventBus();

// Eventos padrão do sistema
export const SINGLE_SPA_EVENTS = {
  // Eventos de dados
  DATA_SAVED: 'data:saved',
  DATA_LOADED: 'data:loaded',
  DATA_UPDATED: 'data:updated',
  DATA_DELETED: 'data:deleted',

  // Eventos de UI
  UI_STATE_CHANGED: 'ui:state_changed',
  MODAL_OPENED: 'ui:modal_opened',
  MODAL_CLOSED: 'ui:modal_closed',
  FORM_SUBMITTED: 'ui:form_submitted',

  // Eventos de navegação
  ROUTE_CHANGED: 'navigation:route_changed',
  PAGE_LOADED: 'navigation:page_loaded',

  // Eventos de autenticação
  USER_AUTHENTICATED: 'auth:user_authenticated',
  USER_LOGOUT: 'auth:user_logout',
  SESSION_EXPIRED: 'auth:session_expired',

  // Eventos de certificado
  CERTIFICADO_CHANGED: 'certificado:changed',
  CERTIFICADO_LOADED: 'certificado:loaded',

  // Eventos de contribuição
  CONTRIBUICAO_TOGGLED: 'contribuicao:toggled',
  CONTRIBUICAO_SAVED: 'contribuicao:saved',
  CONTRIBUICAO_CONFIRMED: 'contribuicao:confirmed',
} as const;

export { eventBus };
export type { IEventBusEvent, IEventBusListener };
