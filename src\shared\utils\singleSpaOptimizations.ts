/**
 * Otimizações simples e robustas para Single SPA
 * Foco em evitar re-renderizações desnecessárias
 */

import React from 'react';
import { isDevelopment } from './environmentUtils';

/**
 * Wrapper simples para memoização de componentes
 */
export const withSingleSpaOptimization = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  componentName?: string
): React.ComponentType<T> => {
  const MemoizedComponent = React.memo(Component);
  
  if (componentName) {
    MemoizedComponent.displayName = `Optimized(${componentName})`;
  }
  
  return MemoizedComponent;
};

/**
 * Hook para debounce de atualizações
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook para evitar re-renders desnecessários
 */
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  return React.useCallback(callback, deps);
};

/**
 * Hook para valores estáveis
 */
export const useStableValue = <T>(value: T): T => {
  const ref = React.useRef<T>(value);
  
  // Apenas atualiza se o valor realmente mudou
  if (JSON.stringify(ref.current) !== JSON.stringify(value)) {
    ref.current = value;
  }
  
  return ref.current;
};

/**
 * Configuração básica para Single SPA
 */
export const configureSingleSpaOptimizations = () => {
  if (!isDevelopment()) {
    return;
  }

  // Log de inicialização
  console.log('[Single SPA] Otimizações ativadas para desenvolvimento');

  // Configurar observador de performance
  if (typeof window !== 'undefined' && window.performance) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'measure' && entry.name.includes('React')) {
          console.log(`[Performance] ${entry.name}: ${entry.duration.toFixed(2)}ms`);
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['measure'] });
    } catch (error) {
      // Ignorar se não suportado
    }
  }
};

/**
 * Utilitário para log de re-renders
 */
export const useRenderLogger = (componentName: string) => {
  const renderCount = React.useRef(0);
  
  React.useEffect(() => {
    renderCount.current += 1;
    
    if (isDevelopment()) {
      console.log(`[Render] ${componentName} renderizado ${renderCount.current} vezes`);
    }
  });
  
  return renderCount.current;
};

/**
 * Hook para detectar mudanças de props
 */
export const usePropsLogger = <T extends Record<string, any>>(
  props: T,
  componentName: string
) => {
  const prevProps = React.useRef<T>();
  
  React.useEffect(() => {
    if (isDevelopment() && prevProps.current) {
      const changedProps: string[] = [];
      
      Object.keys(props).forEach((key) => {
        if (props[key] !== prevProps.current![key]) {
          changedProps.push(key);
        }
      });
      
      if (changedProps.length > 0) {
        console.log(`[Props] ${componentName} props alteradas:`, changedProps);
      }
    }
    
    prevProps.current = props;
  });
};

/**
 * Componente wrapper para debug de re-renders
 */
export const RenderDebugger: React.FC<{
  name: string;
  children: React.ReactNode;
}> = ({ name, children }) => {
  useRenderLogger(name);
  
  return <>{children}</>;
};

/**
 * Hook para otimizar listas
 */
export const useOptimizedList = <T extends { id: string | number }>(
  items: T[]
): T[] => {
  return React.useMemo(() => items, [JSON.stringify(items.map(item => item.id))]);
};

/**
 * Utilitário para comparação shallow
 */
export const shallowEqual = (obj1: any, obj2: any): boolean => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

/**
 * Hook para valores memoizados com comparação customizada
 */
export const useMemoWithComparison = <T>(
  factory: () => T,
  deps: React.DependencyList,
  compare: (prev: React.DependencyList, next: React.DependencyList) => boolean = shallowEqual
): T => {
  const ref = React.useRef<{ deps: React.DependencyList; value: T }>();
  
  if (!ref.current || !compare(ref.current.deps, deps)) {
    ref.current = {
      deps,
      value: factory(),
    };
  }
  
  return ref.current.value;
};

/**
 * Configuração para desenvolvimento
 */
export const DEV_CONFIG = {
  enableRenderLogging: isDevelopment(),
  enablePropsLogging: isDevelopment(),
  enablePerformanceMonitoring: isDevelopment(),
  debounceTime: 100,
} as const;
