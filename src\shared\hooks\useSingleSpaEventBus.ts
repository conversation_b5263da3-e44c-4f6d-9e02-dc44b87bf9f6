import { useEffect, useCallback, useRef } from 'react';
import {
  eventBus,
  IEventBusListener,
  SINGLE_SPA_EVENTS,
} from '../utils/singleSpaEventBus';

/**
 * Hook para usar o Event Bus em componentes React
 * Evita re-renderizações desnecessárias ao comunicar entre partes do MFE
 */
export const useSingleSpaEventBus = () => {
  const listenersRef = useRef<(() => void)[]>([]);

  // Limpar listeners ao desmontar componente
  useEffect(() => {
    return () => {
      listenersRef.current.forEach(unsubscribe => unsubscribe());
      listenersRef.current = [];
    };
  }, []);

  /**
   * Registra um listener para um evento específico
   */
  const on = useCallback((eventType: string, listener: IEventBusListener) => {
    const unsubscribe = eventBus.on(eventType, listener);
    listenersRef.current.push(unsubscribe);
    return unsubscribe;
  }, []);

  /**
   * Emite um evento
   */
  const emit = useCallback(
    (eventType: string, payload?: any, source?: string) => {
      eventBus.emit(eventType, payload, source);
    },
    [],
  );

  /**
   * Emite um evento com debounce
   */
  const emitOnce = useCallback(
    (eventType: string, payload?: any, source?: string, delay?: number) => {
      eventBus.emitOnce(eventType, payload, source, delay);
    },
    [],
  );

  /**
   * Remove um listener específico
   */
  const off = useCallback((eventType: string, listener: IEventBusListener) => {
    eventBus.off(eventType, listener);
  }, []);

  return {
    on,
    emit,
    emitOnce,
    off,
    events: SINGLE_SPA_EVENTS,
  };
};

/**
 * Hook específico para eventos de dados
 * Facilita a comunicação quando dados são salvos/carregados
 */
export const useDataEvents = () => {
  const { on, emit, events } = useSingleSpaEventBus();

  const onDataSaved = useCallback(
    (listener: (data: any) => void) => {
      return on(events.DATA_SAVED, event => listener(event.payload));
    },
    [on, events.DATA_SAVED],
  );

  const onDataLoaded = useCallback(
    (listener: (data: any) => void) => {
      return on(events.DATA_LOADED, event => listener(event.payload));
    },
    [on, events.DATA_LOADED],
  );

  const onDataUpdated = useCallback(
    (listener: (data: any) => void) => {
      return on(events.DATA_UPDATED, event => listener(event.payload));
    },
    [on, events.DATA_UPDATED],
  );

  const emitDataSaved = useCallback(
    (data: any, source?: string) => {
      emit(events.DATA_SAVED, data, source);
    },
    [emit, events.DATA_SAVED],
  );

  const emitDataLoaded = useCallback(
    (data: any, source?: string) => {
      emit(events.DATA_LOADED, data, source);
    },
    [emit, events.DATA_LOADED],
  );

  const emitDataUpdated = useCallback(
    (data: any, source?: string) => {
      emit(events.DATA_UPDATED, data, source);
    },
    [emit, events.DATA_UPDATED],
  );

  return {
    onDataSaved,
    onDataLoaded,
    onDataUpdated,
    emitDataSaved,
    emitDataLoaded,
    emitDataUpdated,
  };
};

/**
 * Hook específico para eventos de UI
 * Facilita a comunicação de mudanças de estado da interface
 */
export const useUIEvents = () => {
  const { on, emit, events } = useSingleSpaEventBus();

  const onUIStateChanged = useCallback(
    (listener: (state: any) => void) => {
      return on(events.UI_STATE_CHANGED, event => listener(event.payload));
    },
    [on, events.UI_STATE_CHANGED],
  );

  const onFormSubmitted = useCallback(
    (listener: (formData: any) => void) => {
      return on(events.FORM_SUBMITTED, event => listener(event.payload));
    },
    [on, events.FORM_SUBMITTED],
  );

  const emitUIStateChanged = useCallback(
    (state: any, source?: string) => {
      emit(events.UI_STATE_CHANGED, state, source);
    },
    [emit, events.UI_STATE_CHANGED],
  );

  const emitFormSubmitted = useCallback(
    (formData: any, source?: string) => {
      emit(events.FORM_SUBMITTED, formData, source);
    },
    [emit, events.FORM_SUBMITTED],
  );

  return {
    onUIStateChanged,
    onFormSubmitted,
    emitUIStateChanged,
    emitFormSubmitted,
  };
};

/**
 * Hook específico para eventos de contribuição
 * Facilita a comunicação específica do módulo de ativação/suspensão
 */
export const useContribuicaoEvents = () => {
  const { on, emit, events } = useSingleSpaEventBus();

  const onContribuicaoToggled = useCallback(
    (listener: (data: { index: number; ativo: boolean }) => void) => {
      return on(events.CONTRIBUICAO_TOGGLED, event => listener(event.payload));
    },
    [on, events.CONTRIBUICAO_TOGGLED],
  );

  const onContribuicaoSaved = useCallback(
    (listener: (data: any) => void) => {
      return on(events.CONTRIBUICAO_SAVED, event => listener(event.payload));
    },
    [on, events.CONTRIBUICAO_SAVED],
  );

  const onContribuicaoConfirmed = useCallback(
    (listener: (data: any) => void) => {
      return on(events.CONTRIBUICAO_CONFIRMED, event =>
        listener(event.payload),
      );
    },
    [on, events.CONTRIBUICAO_CONFIRMED],
  );

  const emitContribuicaoToggled = useCallback(
    (index: number, ativo: boolean, source?: string) => {
      emit(events.CONTRIBUICAO_TOGGLED, { index, ativo }, source);
    },
    [emit, events.CONTRIBUICAO_TOGGLED],
  );

  const emitContribuicaoSaved = useCallback(
    (data: any, source?: string) => {
      emit(events.CONTRIBUICAO_SAVED, data, source);
    },
    [emit, events.CONTRIBUICAO_SAVED],
  );

  const emitContribuicaoConfirmed = useCallback(
    (data: any, source?: string) => {
      emit(events.CONTRIBUICAO_CONFIRMED, data, source);
    },
    [emit, events.CONTRIBUICAO_CONFIRMED],
  );

  return {
    onContribuicaoToggled,
    onContribuicaoSaved,
    onContribuicaoConfirmed,
    emitContribuicaoToggled,
    emitContribuicaoSaved,
    emitContribuicaoConfirmed,
  };
};
