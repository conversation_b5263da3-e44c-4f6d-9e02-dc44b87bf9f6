const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");
const path = require("path");
const webpack = require("webpack");
const dotenv = require("dotenv");

module.exports = (webpackConfigEnv, argv) => {
  let environment = webpackConfigEnv.production
  ? 'production'
  : webpackConfigEnv.homologation
  ? 'homologation'
  : webpackConfigEnv['pre-production']
  ? 'pre-production'
  : 'development';


  const envFile = `.env.${environment}`;
  dotenv.config({ path: envFile });

  const defaultConfig = singleSpaDefaults({
    orgName: "CVP",
    projectName: process.env.REACT_APP_NOME_MFE,
    webpackConfigEnv,
    argv,
  });

  const config = {
    CLIENT_ID: process.env.REACT_APP_CLIENT_ID,
    NOME_ACESSO_STORE_KEY: process.env.REACT_APP_NOME_ACESSO_STORE_KEY,
    BEARER_TOKEN_STORE_KEY: process.env.REACT_APP_BEARER_TOKEN_STORE_KEY,
    USER_METADATA_STOREY_KEY: process.env.REACT_APP_USER_METADATA_STOREY_KEY,
    CACHE_DURATION: process.env.REACT_APP_CACHE_DURATION,
    API_BASE_URL: process.env.REACT_APP_API_BASE_URL,
    MFE_ENV: process.env.REACT_APP_PE_ENV,
    CHAT_BASE_URL: process.env.REACT_APP_CHAT_BASE_URL,
    WEBCHAT_BASE_URL: process.env.REACT_APP_WEBCHAT_BASE_URL,
    REACT_APP_MFE_ASSINATURA_NAME: process.env.REACT_APP_MFE_ASSINATURA_NAME,
    REACT_APP_INSIGHTS_CONNECTION_STRING: process.env.REACT_APP_INSIGHTS_CONNECTION_STRING,
    REACT_APP_NOME_MFE: process.env.REACT_APP_NOME_MFE,
    REACT_APP_ENABLE_STRICT_MODE: process.env.REACT_APP_ENABLE_STRICT_MODE,
  };

  return merge(defaultConfig, {
    // Configurações específicas para Single SPA HMR
    mode: environment === 'development' ? 'development' : 'production',
    devtool: environment === 'development' ? 'eval-source-map' : false,

    module: {
      rules: [

      ],
    },
    resolve: {
      alias: {
        '@src': path.resolve(__dirname, 'src'),
        'react': path.resolve(__dirname, 'node_modules/react'),
        'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
      },
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    },
    plugins: [
      new webpack.DefinePlugin({
        'AppConfig': JSON.stringify(config),
        // Definir process.env para Single SPA
        'process.env.NODE_ENV': JSON.stringify(environment),
        'process.env.REACT_APP_PE_ENV': JSON.stringify(process.env.REACT_APP_PE_ENV),
        'process.env.REACT_APP_ENABLE_STRICT_MODE': JSON.stringify(process.env.REACT_APP_ENABLE_STRICT_MODE || 'false'),
      }),
    ],
    externals: {},
    optimization: {
      // Otimizações para Single SPA - evitar re-renderizações
      usedExports: true,
      sideEffects: false,
      splitChunks: false,
      runtimeChunk: false,
    },
    // Configurações específicas para desenvolvimento
    ...(environment === 'development' && {
      devServer: {
        hot: true,
        liveReload: false, // Desabilitar live reload para usar apenas HMR
        client: {
          overlay: {
            errors: true,
            warnings: false, // Reduzir ruído
          },
        },
      },
    }),
    ignoreWarnings: [{
      module: /@cvp\/componentes-posvenda/,
      message: /Critical dependency/,
    }],
  });
};
