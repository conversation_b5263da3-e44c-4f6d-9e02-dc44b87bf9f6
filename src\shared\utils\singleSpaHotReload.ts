/**
 * Utilitários para otimizar Hot Reload em Single SPA
 * Evita re-renderizações completas durante desenvolvimento
 */

import { isDevelopment } from './environmentUtils';

interface IHotReloadConfig {
  debounceTime: number;
  maxRetries: number;
  enableLogging: boolean;
}

const DEFAULT_CONFIG: IHotReloadConfig = {
  debounceTime: 100,
  maxRetries: 3,
  enableLogging: true,
};

class SingleSpaHotReload {
  private config: IHotReloadConfig;
  private pendingUpdates: Map<string, NodeJS.Timeout> = new Map();
  private updateQueue: Set<string> = new Set();
  private isProcessing = false;

  constructor(config: Partial<IHotReloadConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Debounce de atualizações para evitar re-renders múltiplos
   */
  debounceUpdate(key: string, callback: () => void): void {
    if (!isDevelopment()) {
      callback();
      return;
    }

    // Limpar timeout anterior se existir
    const existingTimeout = this.pendingUpdates.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Criar novo timeout
    const timeout = setTimeout(() => {
      this.pendingUpdates.delete(key);
      this.updateQueue.delete(key);
      
      if (this.config.enableLogging) {
        console.log(`[HMR] Executing debounced update: ${key}`);
      }
      
      callback();
    }, this.config.debounceTime);

    this.pendingUpdates.set(key, timeout);
    this.updateQueue.add(key);
  }

  /**
   * Processa fila de atualizações em batch
   */
  async processBatchUpdates(): Promise<void> {
    if (this.isProcessing || this.updateQueue.size === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const updates = Array.from(this.updateQueue);
      
      if (this.config.enableLogging) {
        console.log(`[HMR] Processing batch updates:`, updates);
      }

      // Processar atualizações em batch
      await Promise.all(
        updates.map(async (key) => {
          const timeout = this.pendingUpdates.get(key);
          if (timeout) {
            clearTimeout(timeout);
            this.pendingUpdates.delete(key);
          }
        })
      );

      this.updateQueue.clear();
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Limpa todas as atualizações pendentes
   */
  clearPendingUpdates(): void {
    this.pendingUpdates.forEach((timeout) => {
      clearTimeout(timeout);
    });
    
    this.pendingUpdates.clear();
    this.updateQueue.clear();
    this.isProcessing = false;

    if (this.config.enableLogging) {
      console.log('[HMR] Cleared all pending updates');
    }
  }

  /**
   * Obtém estatísticas das atualizações
   */
  getStats(): {
    pendingUpdates: number;
    queuedUpdates: number;
    isProcessing: boolean;
  } {
    return {
      pendingUpdates: this.pendingUpdates.size,
      queuedUpdates: this.updateQueue.size,
      isProcessing: this.isProcessing,
    };
  }
}

// Instância singleton
const hotReload = new SingleSpaHotReload();

/**
 * Hook para usar hot reload otimizado
 */
export const useOptimizedHotReload = () => {
  const debounceUpdate = (key: string, callback: () => void) => {
    hotReload.debounceUpdate(key, callback);
  };

  const processBatch = () => {
    hotReload.processBatchUpdates();
  };

  const clearPending = () => {
    hotReload.clearPendingUpdates();
  };

  const getStats = () => {
    hotReload.getStats();
  };

  return {
    debounceUpdate,
    processBatch,
    clearPending,
    getStats,
  };
};

/**
 * Configurar HMR para Single SPA
 */
export const configureSingleSpaHMR = () => {
  if (!isDevelopment() || typeof module === 'undefined') {
    return;
  }

  // Configurar Webpack HMR
  if (module.hot) {
    module.hot.accept();
    
    // Limpar atualizações pendentes ao recarregar
    module.hot.dispose(() => {
      hotReload.clearPendingUpdates();
    });

    // Log de HMR ativo
    console.log('[HMR] Single SPA Hot Module Replacement ativo');
  }
};

/**
 * Wrapper para componentes que devem evitar re-renders desnecessários
 */
export const withHotReloadOptimization = <T extends object>(
  Component: React.ComponentType<T>,
  componentName: string
): React.ComponentType<T> => {
  if (!isDevelopment()) {
    return Component;
  }

  return React.memo(Component, (prevProps, nextProps) => {
    // Comparação shallow das props
    const prevKeys = Object.keys(prevProps as object);
    const nextKeys = Object.keys(nextProps as object);

    if (prevKeys.length !== nextKeys.length) {
      return false;
    }

    for (const key of prevKeys) {
      if ((prevProps as any)[key] !== (nextProps as any)[key]) {
        console.log(`[HMR] ${componentName} prop changed:`, key);
        return false;
      }
    }

    console.log(`[HMR] ${componentName} props unchanged, skipping render`);
    return true;
  });
};

export { hotReload };
export default SingleSpaHotReload;
