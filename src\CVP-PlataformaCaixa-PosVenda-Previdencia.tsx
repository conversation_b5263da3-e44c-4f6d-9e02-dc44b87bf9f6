import React from 'react';
import ReactDOMClient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import App from './App';
import { isDevelopment } from './shared/utils/environmentUtils';

// Configuração otimizada para Single SPA
const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  suppressComponentDidCatchWarning: true,
  domElementGetter: () => {
    // Reutilizar elemento DOM se existir (evita re-mount completo)
    let domElement = document.getElementById(
      'CVP-PlataformaCaixa-PosVenda-Previdencia',
    );
    if (!domElement) {
      domElement = document.createElement('div');
      domElement.id = 'CVP-PlataformaCaixa-PosVenda-Previdencia';
    }
    return domElement;
  },
  errorBoundary(err, info) {
    if (isDevelopment()) {
      console.error('Single SPA Error:', err, info);
    }
    return (
      <>
        <h1>Oop! 500</h1>
        <pre>{err.stack}</pre>
        <pre>{info.componentStack}</pre>
      </>
    );
  },
  // Otimizações para evitar re-renders
  renderType: 'createRoot', // Usar React 18 createRoot
  parcelCanUpdate: false, // Evitar updates desnecessários
});

export const { bootstrap, mount, unmount } = lifecycles;
