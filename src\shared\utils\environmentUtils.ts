/**
 * Utilitários para verificação de ambiente em Single SPA
 * Evita erros de "process is not defined" no browser
 */

interface IAppConfig {
  MFE_ENV?: string;
  REACT_APP_ENABLE_STRICT_MODE?: string;
  CLIENT_ID?: string;
  API_BASE_URL?: string;
  [key: string]: any;
}

/**
 * Obtém a configuração da aplicação de forma segura
 */
export const getAppConfig = (): IAppConfig => {
  try {
    // Primeiro tenta AppConfig injetado pelo webpack
    if (typeof window !== 'undefined' && (window as any).AppConfig) {
      return (window as any).AppConfig;
    }

    // Fallback para process.env se disponível
    if (typeof process !== 'undefined' && process.env) {
      return {
        MFE_ENV: process.env.REACT_APP_PE_ENV,
        REACT_APP_ENABLE_STRICT_MODE: process.env.REACT_APP_ENABLE_STRICT_MODE,
        CLIENT_ID: process.env.REACT_APP_CLIENT_ID,
        API_BASE_URL: process.env.REACT_APP_API_BASE_URL,
      };
    }

    // Fallback padrão
    return {};
  } catch (error) {
    console.warn('Erro ao obter configuração da aplicação:', error);
    return {};
  }
};

/**
 * Verifica se está em ambiente de desenvolvimento
 */
export const isDevelopment = (): boolean => {
  const config = getAppConfig();
  return config.MFE_ENV === 'development';
};

/**
 * Verifica se está em ambiente de produção
 */
export const isProduction = (): boolean => {
  const config = getAppConfig();
  return config.MFE_ENV === 'production';
};

/**
 * Verifica se está em ambiente de homologação
 */
export const isHomologation = (): boolean => {
  const config = getAppConfig();
  return config.MFE_ENV === 'homologation';
};

/**
 * Verifica se está em ambiente de pré-produção
 */
export const isPreProduction = (): boolean => {
  const config = getAppConfig();
  return config.MFE_ENV === 'pre-production';
};

/**
 * Obtém o ambiente atual
 */
export const getCurrentEnvironment = (): string => {
  const config = getAppConfig();
  return config.MFE_ENV || 'unknown';
};

/**
 * Verifica se o StrictMode deve ser habilitado
 */
export const shouldEnableStrictMode = (): boolean => {
  const config = getAppConfig();
  return isDevelopment() && config.REACT_APP_ENABLE_STRICT_MODE === 'true';
};

/**
 * Obtém uma variável de configuração específica
 */
export const getConfigValue = (key: string, defaultValue?: any): any => {
  const config = getAppConfig();
  return config[key] ?? defaultValue;
};

/**
 * Verifica se está rodando no browser
 */
export const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

/**
 * Verifica se está rodando em Single SPA
 */
export const isSingleSpa = (): boolean => {
  return isBrowser() && !!(window as any).singleSpaNavigate;
};

/**
 * Log seguro para desenvolvimento
 */
export const devLog = (message: string, ...args: any[]): void => {
  if (isDevelopment()) {
    console.log(`[${getCurrentEnvironment().toUpperCase()}] ${message}`, ...args);
  }
};

/**
 * Warning seguro para desenvolvimento
 */
export const devWarn = (message: string, ...args: any[]): void => {
  if (isDevelopment()) {
    console.warn(`[${getCurrentEnvironment().toUpperCase()}] ${message}`, ...args);
  }
};

/**
 * Error log sempre ativo
 */
export const errorLog = (message: string, error?: any): void => {
  console.error(`[${getCurrentEnvironment().toUpperCase()}] ${message}`, error);
};

/**
 * Obtém informações de debug do ambiente
 */
export const getEnvironmentInfo = () => {
  const config = getAppConfig();
  
  return {
    environment: getCurrentEnvironment(),
    isDevelopment: isDevelopment(),
    isProduction: isProduction(),
    isBrowser: isBrowser(),
    isSingleSpa: isSingleSpa(),
    shouldEnableStrictMode: shouldEnableStrictMode(),
    config: config,
    userAgent: isBrowser() ? navigator.userAgent : 'N/A',
    url: isBrowser() ? window.location.href : 'N/A',
  };
};

// Exportar constantes de ambiente
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  HOMOLOGATION: 'homologation',
  PRE_PRODUCTION: 'pre-production',
  PRODUCTION: 'production',
} as const;

export type TEnvironment = typeof ENVIRONMENTS[keyof typeof ENVIRONMENTS];
