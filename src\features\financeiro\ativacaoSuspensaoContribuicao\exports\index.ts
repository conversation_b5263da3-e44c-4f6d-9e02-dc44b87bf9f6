export {
  formatarValorPadraoBrasileiro,
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from '@cvp/utils';

export {
  Text,
  Switch,
  Button,
  Grid,
  GridItem,
  ConditionalRenderer,
  Alert,
  LoadingSpinner,
} from '@cvp/design-system-caixa';

export { Match, SwitchCase } from '@cvp/componentes-posvenda';

export { AtivacaoSuspensaoContribuicaoProvider } from '../context/AtivacaoSuspensaoContribuicaoContext';
export { useAtivacaoSuspensaoContribuicaoContext } from '../context/AtivacaoSuspensaoContribuicaoContext';

export { TabelaContribuicoes } from '../components/TabelaContribuicoes/TabelaContribuicoes';
export { SolicitacaoAtivacaoSuspensao } from '../views/SolicitacaoAtivacaoSuspensao';

export { useFormularioAtivacaoSuspensao } from '../hooks/useFormularioAtivacaoSuspensao';

export type { IContribuicaoItem } from '../types/IContribuicaoItem';
export type { IAtivacaoSuspensaoContribuicaoData } from '../types/IAtivacaoSuspensaoContribuicaoData';

export { useState, useContext } from 'react';
export type { ReactNode } from 'react';
