# Otimizações Single SPA - Evitando Re-renderizações Completas do MFE

## 🎯 **Problema Identificado**

Em arquiteturas Single SPA, quando dados são salvos, o MFE inteiro re-renderiza devido a:

1. **React.StrictMode** causando re-renderizações duplas
2. **Contexts globais** propagando mudanças para toda a árvore de componentes
3. **Interceptors de API** disparando atualizações globais
4. **Mudanças no sessionStorage/localStorage** sendo observadas globalmente
5. **Comunicação inadequada** entre partes do microfrontend

## 🚀 **Soluções Implementadas**

### **1. React.StrictMode Condicional**

**Arquivo**: `src/App.tsx`

```typescript
// StrictMode removido em produção para evitar re-renderizações duplas
{process.env.NODE_ENV === 'development' && process.env.REACT_APP_ENABLE_STRICT_MODE === 'true' ? (
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
) : (
  <RouterProvider router={router} />
)}
```

**Benefícios**:
- Elimina re-renderizações duplas em produção
- Mantém StrictMode opcional em desenvolvimento
- Melhora performance em Single SPA

### **2. Event Bus Otimizado para Single SPA**

**Arquivo**: `src/shared/utils/singleSpaEventBus.ts`

**Funcionalidades**:
- Comunicação entre componentes sem re-renderizar toda a árvore
- Debounce automático para evitar múltiplos eventos
- Histórico de eventos para debug
- Tipagem forte com TypeScript

**Eventos Padrão**:
```typescript
export const SINGLE_SPA_EVENTS = {
  DATA_SAVED: 'data:saved',
  DATA_LOADED: 'data:loaded',
  CONTRIBUICAO_TOGGLED: 'contribuicao:toggled',
  CONTRIBUICAO_SAVED: 'contribuicao:saved',
  // ... outros eventos
};
```

**Uso**:
```typescript
// Emitir evento
eventBus.emit(SINGLE_SPA_EVENTS.DATA_SAVED, data, 'source');

// Escutar evento
eventBus.on(SINGLE_SPA_EVENTS.DATA_SAVED, (event) => {
  console.log('Dados salvos:', event.payload);
});
```

### **3. Hooks React para Event Bus**

**Arquivo**: `src/shared/hooks/useSingleSpaEventBus.ts`

**Hooks Disponíveis**:
- `useSingleSpaEventBus()` - Hook genérico
- `useDataEvents()` - Eventos de dados
- `useUIEvents()` - Eventos de UI
- `useContribuicaoEvents()` - Eventos específicos de contribuição

**Exemplo de Uso**:
```typescript
const { emitContribuicaoToggled } = useContribuicaoEvents();

const handleToggle = (index: number, ativo: boolean) => {
  // Atualizar estado local
  toggleContribuicao(index);
  
  // Comunicar mudança sem re-renderizar tudo
  emitContribuicaoToggled(index, ativo, 'ativacao-suspensao');
};
```

### **4. Context Otimizado**

**Arquivo**: `src/corporativo/context/PrevidenciaContext.tsx`

**Melhorias**:
- useRef para evitar re-renderizações desnecessárias
- useCallback para memoizar funções
- Controle de atualizações simultâneas

```typescript
const isUpdatingRef = useRef(false);

const setCertificadoAtivoInContextAndSession = useCallback(async (data) => {
  if (isUpdatingRef.current) return; // Evitar múltiplas atualizações
  
  isUpdatingRef.current = true;
  try {
    // Lógica de atualização
  } finally {
    isUpdatingRef.current = false;
  }
}, [dependencies]);
```

### **5. Interceptors de API Otimizados**

**Arquivo**: `src/corporativo/infra/config/interceptors/registerMarcadorControleInterceptors.ts`

**Melhorias**:
- Response interceptor que emite eventos em vez de causar re-renderizações
- Debounce para evitar múltiplos eventos
- Comunicação via Event Bus

```typescript
api.interceptors.response.use(response => {
  const method = response.config.method?.toUpperCase();
  
  if (['POST', 'PUT', 'PATCH'].includes(method || '')) {
    // Emitir evento sem causar re-renderização completa
    eventBus.emitOnce(SINGLE_SPA_EVENTS.DATA_SAVED, {
      url: response.config.url,
      method,
      data: response.data,
    }, 'api-interceptor', 50);
  }
  
  return response;
});
```

### **6. Webpack Otimizado**

**Arquivo**: `webpack.config.js`

```javascript
optimization: {
  usedExports: true,      // Tree shaking
  sideEffects: false,     // Eliminar side effects
  splitChunks: false,     // Não dividir chunks (Single SPA)
  runtimeChunk: false,    // Runtime inline
}
```

## 📊 **Resultados Esperados**

### **Antes das Otimizações**:
- ❌ MFE inteiro re-renderiza ao salvar dados
- ❌ Performance degradada
- ❌ Experiência de usuário ruim
- ❌ Perda de estado em alguns casos

### **Depois das Otimizações**:
- ✅ Apenas componentes afetados re-renderizam
- ✅ Performance melhorada significativamente
- ✅ Comunicação eficiente via Event Bus
- ✅ Estado mantido corretamente
- ✅ Experiência de usuário fluida

## 🔧 **Como Usar**

### **1. Configurar Variável de Ambiente**

```bash
# Para habilitar StrictMode em desenvolvimento (opcional)
REACT_APP_ENABLE_STRICT_MODE=true
```

### **2. Usar Event Bus em Componentes**

```typescript
import { useContribuicaoEvents } from '../shared/hooks/useSingleSpaEventBus';

const MyComponent = () => {
  const { emitContribuicaoSaved, onContribuicaoSaved } = useContribuicaoEvents();
  
  // Escutar eventos
  useEffect(() => {
    const unsubscribe = onContribuicaoSaved((data) => {
      console.log('Contribuição salva:', data);
    });
    
    return unsubscribe;
  }, []);
  
  // Emitir eventos
  const handleSave = () => {
    emitContribuicaoSaved(data, 'my-component');
  };
};
```

### **3. Monitorar Performance**

```typescript
// Debug do Event Bus
console.log(eventBus.getStats());
console.log(eventBus.getEventHistory());

// React DevTools
// Ativar "Highlight updates when components render"
```

## 🧪 **Testes**

### **Teste de Performance**:
1. Abrir React DevTools
2. Ativar highlight de re-renderizações
3. Salvar dados
4. Verificar que apenas componentes específicos re-renderizam

### **Teste de Event Bus**:
```typescript
// Teste manual no console
eventBus.emit('test', { message: 'Hello' });
eventBus.getEventHistory('test');
```

## 🚨 **Considerações Importantes**

### **Single SPA Específico**:
- Event Bus é global entre todos os MFEs
- Usar prefixos nos eventos para evitar conflitos
- Limpar listeners ao desmontar componentes

### **Performance**:
- Event Bus tem overhead mínimo
- Debounce evita spam de eventos
- Histórico limitado a 100 eventos

### **Debugging**:
- Eventos são logados no histórico
- Stats disponíveis para monitoramento
- Console warnings para erros

## 📈 **Próximos Passos**

1. **Monitoramento**: Implementar métricas de performance
2. **Testes**: Criar suite de testes automatizados
3. **Documentação**: Treinar equipe nas novas práticas
4. **Expansão**: Aplicar padrões em outros MFEs

## 🎯 **Conclusão**

As otimizações implementadas resolvem o problema de re-renderização completa do MFE em Single SPA através de:

- **Event Bus** para comunicação eficiente
- **Context otimizado** com memoização
- **Interceptors inteligentes** que não causam re-renders
- **Webpack otimizado** para Single SPA
- **StrictMode condicional** para produção

O resultado é uma aplicação Single SPA significativamente mais performática e uma experiência de usuário muito melhor.
