import { getSessionItem } from '@cvp/utils';
import { AxiosInstance } from 'axios';
import {
  eventBus,
  SINGLE_SPA_EVENTS,
} from '../../../../shared/utils/singleSpaEventBus';

export const registerMarcadorControleInterceptors = (
  api: AxiosInstance,
): void => {
  api.interceptors.request.use(async config => {
    const marcadorControle = String(getSessionItem<string>('marcadorControle'));

    if (
      config.url?.includes('PortalEconomiario/') &&
      !config.url?.includes('PECO_AutenticarUsuario')
    ) {
      if (config.data instanceof FormData) {
        config.data.append('marcadorControle', marcadorControle);
      }

      if (!(config.data instanceof FormData)) {
        return {
          ...config,
          data: { ...config.data, marcadorControle },
        };
      }
    }
    return config;
  });

  // Response interceptor para comunicar mudanças sem re-renderizar tudo
  api.interceptors.response.use(
    response => {
      // Detectar operações de salvamento/atualização
      const method = response.config.method?.toUpperCase();
      const url = response.config.url || '';

      if (['POST', 'PUT', 'PATCH'].includes(method || '')) {
        // Emitir evento de dados salvos sem causar re-renderização completa
        eventBus.emitOnce(
          SINGLE_SPA_EVENTS.DATA_SAVED,
          {
            url,
            method,
            data: response.data,
            status: response.status,
          },
          'api-interceptor',
          50, // Debounce de 50ms para evitar múltiplos eventos
        );
      }

      return response;
    },
    error => {
      // Log de erros sem causar re-renderizações
      console.warn('API Error intercepted:', error);
      return Promise.reject(error);
    },
  );
};
